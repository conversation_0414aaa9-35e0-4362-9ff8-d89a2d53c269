.add {
  padding: 2rem;
  background: var(--light-gray);
  min-height: calc(100vh - 80px);
}

.add .card {
  max-width: 800px;
  margin: 0 auto;
}

.add .card-header {
  text-align: center;
}

.add .card-title {
  font-size: 1.75rem;
  color: var(--accent-black);
}

.add .card-subtitle {
  color: var(--dark-gray);
  margin-top: 0.5rem;
}

.add form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Image Upload Section */
.add-img-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.add-img-upload label {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  border: 2px dashed var(--medium-gray);
  border-radius: var(--radius-lg);
  background: var(--white);
  transition: var(--transition);
  min-height: 200px;
  justify-content: center;
}

.add-img-upload label:hover {
  border-color: var(--primary-red);
  background: var(--primary-red-light);
}

.add-img-upload img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.add-img-upload input[type="file"] {
  display: none;
}

.upload-text {
  text-align: center;
  color: var(--dark-gray);
}

.upload-text h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0 0 0.5rem 0;
}

.upload-text p {
  font-size: 0.875rem;
  margin: 0;
}

/* Form Fields */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.add-product-name,
.add-product-description {
  width: 100%;
}

.add-product-name input,
.add-product-description textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
  font-family: inherit;
}

.add-product-name input:focus,
.add-product-description textarea:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.add-product-description textarea {
  resize: vertical;
  min-height: 120px;
}

.add-category-price {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.add-discount,
.add-discount-label,
.add-tags {
  width: 100%;
}

.add-discount input,
.add-discount-label input,
.add-tags input {
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

.add-discount input:focus,
.add-discount-label input:focus,
.add-tags input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

/* Checkboxes */
.add-checkboxes {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--accent-black);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-red);
  cursor: pointer;
}

.checkmark {
  position: relative;
}

/* Form Submit */
.form-submit {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.add-category select,
.add-restaurant select,
.add-price input {
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

.add-category select:focus,
.add-restaurant select:focus,
.add-price input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.add-restaurant {
  width: 100%;
}

.form-help {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--dark-gray);
  line-height: 1.4;
}

/* Submit Button */
.add-btn {
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
  color: var(--white);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.add-btn:active {
  transform: translateY(0);
}

.add-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Form Labels */
.form-label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--accent-black);
  font-size: 1rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--danger);
}

/* Responsive Design */
@media (max-width: 768px) {
  .add {
    padding: 1rem;
  }

  .form-row,
  .add-category-price {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .add-checkboxes {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .add-img-upload label {
    padding: 1.5rem;
    min-height: 150px;
  }

  .add-img-upload img {
    width: 120px;
    height: 120px;
  }

  .add .card-title {
    font-size: 1.5rem;
  }
}

/* Cloudinary Upload Styles */
.upload-actions {
  margin-top: 10px;
  text-align: center;
}

.upload-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.upload-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.upload-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.upload-success {
  margin-top: 10px;
  text-align: center;
}

.success-text {
  color: #27ae60;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.cloudinary-url {
  margin-top: 5px;
}

.cloudinary-url small {
  color: #666;
  font-size: 12px;
  word-break: break-all;
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.error-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-fallback .upload-text {
  color: #f39c12;
}

.error-fallback .upload-text h3 {
  color: #f39c12;
}

.test-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.test-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}