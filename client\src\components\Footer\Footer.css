.footer{
    color: #d9d9d9;
    background-color: #323232;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 16px 8vw;
    padding-top: 40px;
    margin-top: 60px;
}
.footer-content{
    width: 100%;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.footer-content-left, .footer-content-center {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 12px;
}

.footer-links-container {
    display: flex;
    gap: 40px;
    width: 100%;
}

.footer-company, .footer-contact {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 8px;
    flex: 1;
}

.footer-content-left img {
    width: 100px;
    height: auto;
    margin-bottom: 4px;
}

.footer-content-left p {
    font-size: 13px;
    line-height: 1.4;
    color: #d9d9d9;
    margin-bottom: 6px;
}

.footer-content-left li, .footer-company li, .footer-contact li{
    list-style: none;
    margin-bottom: 6px;
    cursor: pointer;
    transition: color 0.3s ease;
    font-size: 13px;
}

.footer-content-left li:hover, .footer-company li:hover, .footer-contact li:hover {
    color: #ff6b35;
}

.footer-company h2, .footer-contact h2{
    color: white;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.3px;
}

.footer-social-icons {
    display: flex;
    gap: 6px;
    margin-top: 6px;
}

.footer-social-icons img{
    width: 30px;
    margin-right: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    border-radius: 6px;
    cursor: pointer;
}

.footer-social-icons img:hover {
    transform: scale(1.1);
    opacity: 0.8;
}
.footer hr{
    width: 100%;
    height: 1px;
    margin: 12px 0;
    background-color: #555;
    border: none;
    opacity: 0.6;
}

.footer-copyright {
    font-size: 12px;
    color: #999;
    text-align: center;
    font-weight: 400;
    letter-spacing: 0.2px;
}
/* Tablet Responsive */
@media (max-width: 1024px) {
    .footer {
        padding: 14px 6vw;
        padding-top: 30px;
        margin-top: 50px;
    }

    .footer-content {
        gap: 30px;
    }

    .footer-content-left, .footer-content-center {
        gap: 10px;
    }

    .footer-links-container {
        gap: 30px;
    }

    .footer-company, .footer-contact {
        gap: 6px;
    }

    .footer-content-left img {
        width: 90px;
    }

    .footer-social-icons img {
        width: 28px;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .footer {
        padding: 12px 4vw;
        padding-top: 24px;
        margin-top: 30px;
        gap: 10px;
    }

    .footer-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .footer-content-left, .footer-content-center {
        gap: 8px;
        align-items: flex-start;
    }

    .footer-links-container {
        display: flex;
        flex-direction: row;
        gap: 20px;
        width: 100%;
    }

    .footer-company, .footer-contact {
        gap: 6px;
        flex: 1;
    }

    .footer-content-left img {
        width: 80px;
        height: auto;
    }

    .footer-content-left p {
        font-size: 12px;
        line-height: 1.3;
        color: #d9d9d9;
    }

    .footer-company h2, .footer-contact h2 {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .footer-content-left li, .footer-company li, .footer-contact li {
        margin-bottom: 4px;
        font-size: 12px;
        transition: color 0.3s ease;
    }

    .footer-content-left li:hover, .footer-company li:hover, .footer-contact li:hover {
        color: #ff6b35;
    }

    .footer-social-icons {
        display: flex;
        gap: 4px;
        margin-top: 4px;
    }

    .footer-social-icons img {
        width: 24px;
        margin-right: 0;
        transition: transform 0.3s ease;
        border-radius: 4px;
    }

    .footer-social-icons img:hover {
        transform: scale(1.1);
    }

    .footer hr {
        margin: 8px 0;
        height: 1px;
    }

    .footer-copyright {
        text-align: center;
        font-size: 10px;
        color: #999;
    }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
    .footer {
        padding: 10px 3vw;
        padding-top: 20px;
        margin-top: 25px;
        gap: 8px;
    }

    .footer-content {
        gap: 12px;
    }

    .footer-content-left, .footer-content-center {
        gap: 6px;
    }

    .footer-links-container {
        flex-direction: row;
        gap: 12px;
    }

    .footer-company, .footer-contact {
        gap: 4px;
    }

    .footer-content-left img {
        width: 70px;
    }

    .footer-content-left p {
        font-size: 11px;
        line-height: 1.2;
    }

    .footer-company h2, .footer-contact h2 {
        font-size: 12px;
        margin-bottom: 3px;
    }

    .footer-content-left li, .footer-company li, .footer-contact li {
        margin-bottom: 3px;
        font-size: 11px;
    }

    .footer-social-icons {
        gap: 3px;
        margin-top: 3px;
    }

    .footer-social-icons img {
        width: 20px;
        border-radius: 3px;
    }

    .footer hr {
        margin: 6px 0;
    }

    .footer-copyright {
        font-size: 9px;
        padding: 0 4px;
        line-height: 1.2;
    }
}

/* Extra Small Mobile */
@media (max-width: 320px) {
    .footer {
        padding: 8px 2vw;
        padding-top: 16px;
        margin-top: 20px;
        gap: 6px;
    }

    .footer-content {
        gap: 10px;
    }

    .footer-content-left, .footer-content-center {
        gap: 4px;
    }

    .footer-links-container {
        gap: 8px;
    }

    .footer-company, .footer-contact {
        gap: 3px;
    }

    .footer-content-left img {
        width: 60px;
    }

    .footer-content-left p {
        font-size: 10px;
        line-height: 1.1;
    }

    .footer-company h2, .footer-contact h2 {
        font-size: 11px;
        margin-bottom: 2px;
    }

    .footer-content-left li, .footer-company li, .footer-contact li {
        font-size: 10px;
        margin-bottom: 2px;
    }

    .footer-social-icons {
        gap: 2px;
        margin-top: 2px;
    }

    .footer-social-icons img {
        width: 18px;
    }

    .footer hr {
        margin: 4px 0;
    }

    .footer-copyright {
        font-size: 8px;
        line-height: 1.1;
    }
}