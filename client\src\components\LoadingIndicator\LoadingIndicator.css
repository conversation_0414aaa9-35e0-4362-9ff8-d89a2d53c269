.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Spinner Styles */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-radius: 50%;
  border-top: 2px solid #ff6b35;
  animation: spin 1s linear infinite;
  position: relative;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

.loading-spinner.medium {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-spinner.large {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.spinner-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: #ff6b35;
  border-radius: 50%;
  animation: pulse 1s ease-in-out infinite;
}

/* Dots Styles */
.loading-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.loading-dots.small .dot {
  width: 4px;
  height: 4px;
}

.loading-dots.medium .dot {
  width: 6px;
  height: 6px;
}

.loading-dots.large .dot {
  width: 8px;
  height: 8px;
}

.dot {
  border-radius: 50%;
  background-color: #ff6b35;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

/* Pulse Styles */
.loading-pulse {
  border-radius: 50%;
  background-color: #ff6b35;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-pulse.small {
  width: 16px;
  height: 16px;
}

.loading-pulse.medium {
  width: 24px;
  height: 24px;
}

.loading-pulse.large {
  width: 32px;
  height: 32px;
}

/* Skeleton Styles */
.skeleton-box {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-box.small {
  width: 60px;
  height: 60px;
}

.skeleton-box.medium {
  width: 80px;
  height: 80px;
}

.skeleton-box.large {
  width: 120px;
  height: 120px;
}

/* Loading Text */
.loading-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-align: center;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .loading-spinner.large {
    width: 28px;
    height: 28px;
  }
  
  .loading-pulse.large {
    width: 28px;
    height: 28px;
  }
  
  .skeleton-box.large {
    width: 100px;
    height: 100px;
  }
}
