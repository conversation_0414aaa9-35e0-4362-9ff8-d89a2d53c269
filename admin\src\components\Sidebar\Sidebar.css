.sidebar {
  width: 280px;
  min-height: calc(100vh - 80px);
  background: var(--white);
  border-right: 1px solid var(--medium-gray);
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 80px;
  overflow-y: auto;
}

.sidebar-header {
  padding: 2rem 1.5rem 1rem;
  border-bottom: 1px solid var(--medium-gray);
}

.sidebar-header h3 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0 0 0.25rem 0;
}

.sidebar-header span {
  font-size: 0.875rem;
  color: var(--dark-gray);
}

.sidebar-menu {
  flex: 1;
  padding: 1rem 0;
}

.sidebar-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  margin: 0.25rem 1rem;
  border-radius: var(--radius-lg);
  color: var(--dark-gray);
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.sidebar-option:hover {
  background: var(--light-gray);
  color: var(--accent-black);
  transform: translateX(4px);
}

.sidebar-option.active {
  background: linear-gradient(135deg, var(--primary-red-light), rgba(255, 71, 87, 0.1));
  color: var(--primary-red);
  border-left: 3px solid var(--primary-red);
  font-weight: 600;
}

.sidebar-option.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-red);
  border-radius: 0 2px 2px 0;
}

.sidebar-option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: transparent;
  transition: var(--transition);
}

.sidebar-option:hover .sidebar-option-icon {
  background: var(--medium-gray);
}

.sidebar-option.active .sidebar-option-icon {
  background: var(--primary-red);
  color: var(--white);
}

.sidebar-option-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.sidebar-option-label {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
}

.sidebar-option-description {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.125rem;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--medium-gray);
  margin-top: auto;
}

.sidebar-help {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--light-gray);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition);
}

.sidebar-help:hover {
  background: var(--medium-gray);
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-red);
  color: var(--white);
  flex-shrink: 0;
}

.help-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--accent-black);
  display: block;
}

.help-subtitle {
  font-size: 0.75rem;
  color: var(--dark-gray);
  display: block;
  margin-top: 0.125rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }

  .sidebar-option {
    padding: 0.875rem 1rem;
  }

  .sidebar-option-description {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 60px;
    position: fixed;
    left: 0;
    top: 80px;
    z-index: 50;
    box-shadow: var(--shadow-lg);
  }

  .sidebar-header,
  .sidebar-footer {
    display: none;
  }

  .sidebar-option {
    padding: 1rem 0.5rem;
    margin: 0.25rem 0.5rem;
    justify-content: center;
    position: relative;
  }

  .sidebar-option-content {
    display: none;
  }

  .sidebar-option-icon {
    width: 36px;
    height: 36px;
  }

  /* Tooltip for mobile */
  .sidebar-option:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--accent-black);
    color: var(--white);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 0.5rem;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease-out forwards;
  }

  .sidebar-option:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: var(--accent-black);
    z-index: 1000;
    margin-left: -5px;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease-out forwards;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    position: fixed;
    left: 0;
    top: 80px;
    bottom: 0;
    z-index: 60;
    box-shadow: var(--shadow-xl);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .sidebar-header,
  .sidebar-footer {
    display: block;
  }

  .sidebar-option {
    padding: 1rem 1.5rem;
    margin: 0.25rem 1rem;
    justify-content: flex-start;
  }

  .sidebar-option-content {
    display: flex;
  }

  .sidebar-option-icon {
    width: 40px;
    height: 40px;
  }

  /* Remove tooltips on very small screens when sidebar is open */
  .sidebar-option:hover::after,
  .sidebar-option:hover::before {
    display: none;
  }
}

@keyframes tooltipFadeIn {
  to {
    opacity: 1;
  }
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--medium-gray);
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}