.cart {
  margin: 100px;
  min-height: 500px; /* Prevent layout shifts */
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-items-title {
  display: grid;
  grid-template-columns: 60px 1.5fr 1fr 1fr 1fr 0.5fr;
  align-items: center;
  color: grey;
  font-size: max(1vw, 12px);
  flex: 1;
}

.clear-cart-btn {
  background-color: #ff6b6b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.clear-cart-btn:hover {
  background-color: #ff5252;
}

.cart-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
  min-height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-cart {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
}

.empty-cart-content {
  text-align: center;
  max-width: 400px;
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.empty-cart h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.empty-cart p {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.browse-menu-btn {
  background-color: #ff6b35;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.browse-menu-btn:hover {
  background-color: #e55a2b;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.cart-items {
  min-height: 200px; /* Prevent content jumping */
}

.cart-items-list {
  width: 100%;
}

.cart-item-wrapper {
  width: 100%;
}

.cart-items-item {
  display: grid;
  grid-template-columns: 60px 1.5fr 1fr 1fr 1fr 0.5fr;
  align-items: center;
  margin: 10px 0px;
  color: black;
}

.cart-items-item img {
  width: 50px;
}

.cart hr {
  height: 1px;
  background-color: #e2e2e2;
  border: none;
}

.cart-items-item .cross {
  cursor: pointer;
}

.cart-bottom {
  margin-top: 80px;
  display: flex;
  justify-content: space-between;
  gap: max(12vw, 20px);
}

.cart-total {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cart-total-details {
  display: flex;
  justify-content: space-between;
  color: #555;
}

.cart-total hr {
  margin: 10px 0px;
}

.cart-total button {
  border: none;
  color: white;
  background-color: tomato;
  width: max(15vw, 200px);
  padding: 12px 0px;
  border-radius: 8px;
  cursor: pointer;
}

.cart-promocode {
  flex: 1;
}

.cart-promocode p {
  color: #555;
}

.cart-promocode-input {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #eaeaea;
  border-radius: 4px;
}

.cart-promocode-input input {
  background: transparent;
  border: none;
  outline: none;
  padding-left: 10px;
}

.cart-promocode-input button {
  width: max(10vw, 150px);
  padding: 12px 5px;
  background-color: black;
  border: none;
  color: white;
  border-radius: 4px;
}

@media (max-width: 750px) {
  .cart {
    margin: 20px;
  }

  .cart-items-title {
    display: none;  /* Hide the header row on small screens */
  }

  .cart-items-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #f9f9f9;
  }

  .cart-items-item img {
    width: 70px;
    margin-bottom: 8px;
  }

  .cart-bottom {
    flex-direction: column-reverse;
  }

  .cart-total button {
    width: 100%;
  }

  .empty-cart {
    min-height: 300px;
    padding: 20px;
  }

  .empty-cart-icon {
    font-size: 3rem;
  }

  .empty-cart h3 {
    font-size: 1.25rem;
  }

  .browse-menu-btn {
    width: 100%;
    padding: 14px 24px;
  }
}
