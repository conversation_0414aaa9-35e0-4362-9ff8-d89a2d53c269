@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap');

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: Outfit;
  scroll-behavior: smooth;
}
body{
  min-height: 100vh;
  overflow-x: hidden; /* Prevent horizontal scroll */
}
a{
  text-decoration: none;
  color: inherit;
}
.app{
  width: 80%;
  margin: auto;
  max-width: 100vw; /* Prevent overflow */
  overflow-x: hidden;
}

/* Responsive container adjustments */
@media (max-width: 1200px) {
  .app {
    width: 90%;
  }
}

@media (max-width: 768px) {
  .app {
    width: 95%;
    padding: 0 4px;
  }
}

@media (max-width: 480px) {
  .app {
    width: 100%;
    padding: 0 4px;
  }
}

@media (max-width: 360px) {
  .app {
    width: 100%;
    padding: 0 2px;
  }
}

@keyframes fadeIn{
  0%{
    opacity: 0;
  }
  100%{
    opacity: 1;
  }
}

/* Global responsive utilities */
.responsive-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.responsive-image {
  max-width: 100%;
  height: auto;
}

/* Prevent horizontal overflow on all elements */
* {
  max-width: 100%;
  box-sizing: border-box;
}

/* Ensure images don't overflow */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent text overflow */
p, h1, h2, h3, h4, h5, h6, span, div {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure containers don't overflow */
.container, .wrapper, .content {
  max-width: 100%;
  overflow-x: hidden;
}

/* Responsive container for very small screens */
@media (max-width: 320px) {
  .app {
    width: 100%;
    padding: 0 1px;
    font-size: 14px;
  }

  /* Reduce font sizes globally for very small screens */
  h1 { font-size: 18px !important; }
  h2 { font-size: 16px !important; }
  h3 { font-size: 14px !important; }
  p { font-size: 12px !important; }

  /* Ensure buttons are touchable */
  button {
    min-height: 32px;
    min-width: 32px;
  }
}