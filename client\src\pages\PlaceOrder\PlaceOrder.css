.place-order{
    display: flex;
    align-items: start;
    justify-content: space-between;
    gap: 50px;
    margin-top: 100px;
}
.place-order-left{
    width: 100%;
    max-width: max(30%,500px);
}
.place-order-left .title{

    font-size: 30px;
    font-weight: 600;
    margin-bottom: 50px;
}
.place-order-left input{
    margin-bottom: 15px;
    width: 100%;
    padding: 10px;
    border: 1px solid #c5c5c5;
    border-radius: 4px;
    outline-color:tomato ;
}
.place-order-left .multi-fields{
    display: flex;
    gap: 10px;
}
.place-order-right{
    width: 100%;
    max-width: max(40%,500px);
}
.place-order .cart-total button{
    margin-top: 30px;
    cursor: pointer;
}

.place-order .cart-total button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}