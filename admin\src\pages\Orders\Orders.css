.orders {
  padding: 2rem;
  background: var(--light-gray);
  min-height: calc(100vh - 80px);
}

.orders .card {
  max-width: 1200px;
  margin: 0 auto;
}

.orders .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.orders .card-title {
  font-size: 1.75rem;
  color: var(--accent-black);
}

.orders .card-subtitle {
  color: var(--dark-gray);
  margin-top: 0.5rem;
}

.orders-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.orders-stat-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.orders-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.stat-icon.processing {
  background: linear-gradient(135deg, var(--warning), #e0a800);
}

.stat-icon.delivery {
  background: linear-gradient(135deg, var(--info), #138496);
}

.stat-icon.delivered {
  background: linear-gradient(135deg, var(--success), #218838);
}

.stat-icon.total {
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
}

.stat-info h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0;
  line-height: 1;
}

.stat-info p {
  color: var(--dark-gray);
  font-size: 0.875rem;
  margin: 0.25rem 0 0 0;
}

.orders-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  background: var(--white);
  font-size: 0.875rem;
  cursor: pointer;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.order-item {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border-left: 4px solid transparent;
}

.order-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.order-item.processing {
  border-left-color: var(--warning);
}

.order-item.delivery {
  border-left-color: var(--info);
}

.order-item.delivered {
  border-left-color: var(--success);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.order-id {
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0 0 0.25rem 0;
}

.order-time {
  font-size: 0.75rem;
  color: var(--dark-gray);
}

.order-status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.order-status-badge.processing {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.order-status-badge.delivery {
  background: rgba(23, 162, 184, 0.1);
  color: var(--info);
}

.order-status-badge.delivered {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.order-content {
  display: grid;
  grid-template-columns: 60px 2fr 1fr 1fr 200px;
  gap: 1.5rem;
  align-items: start;
}

.order-icon {
  width: 60px;
  height: 60px;
  background: var(--light-gray);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-red);
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.order-item-food {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
}

.order-item-name {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
}

.order-item-address {
  color: var(--dark-gray);
  font-size: 0.875rem;
  line-height: 1.4;
}

.order-item-phone {
  color: var(--accent-black);
  font-weight: 500;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.order-summary {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.order-items-count {
  font-size: 0.875rem;
  color: var(--dark-gray);
}

.order-amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-red);
}

.order-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-select {
  padding: 0.5rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  background: var(--white);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.status-select:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.view-details-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--primary-red);
  background: transparent;
  color: var(--primary-red);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  cursor: pointer;
  transition: var(--transition);
}

.view-details-btn:hover {
  background: var(--primary-red);
  color: var(--white);
}

.edit-order-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--info);
  background: transparent;
  color: var(--info);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.edit-order-btn:hover {
  background: var(--info);
  color: var(--white);
}

.delete-order-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #dc3545;
  background: transparent;
  color: #dc3545;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.delete-order-btn:hover {
  background: #dc3545;
  color: var(--white);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 1rem;
}

.no-orders {
  text-align: center;
  padding: 3rem;
  color: var(--dark-gray);
}

.no-orders svg {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
  color: var(--medium-gray);
}

.no-orders h3 {
  font-size: 1.25rem;
  color: var(--accent-black);
  margin: 0 0 0.5rem 0;
}

.no-orders p {
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.edit-modal {
  background: var(--white);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--light-gray);
}

.modal-header h3 {
  margin: 0;
  color: var(--accent-black);
  font-size: 1.25rem;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--dark-gray);
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.close-modal-btn:hover {
  background: var(--light-gray);
  color: var(--accent-black);
}

.modal-content {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
}

.edit-input,
.edit-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: var(--transition);
  box-sizing: border-box;
}

.edit-input:focus,
.edit-select:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.name-inputs,
.address-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.address-inputs {
  grid-template-columns: 1fr 1fr 1fr;
  margin-top: 0.75rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--light-gray);
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--medium-gray);
  background: var(--white);
  color: var(--dark-gray);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.cancel-btn:hover {
  background: var(--light-gray);
  color: var(--accent-black);
}

.save-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  background: var(--primary-red);
  color: var(--white);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.save-btn:hover {
  background: var(--primary-red-hover);
}

/* Responsive */
@media (max-width: 1024px) {
  .order-content {
    grid-template-columns: 50px 1fr 120px;
    gap: 1rem;
  }

  .order-summary,
  .order-item-phone {
    display: none;
  }

  .orders-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .orders {
    padding: 1rem;
  }

  .orders .card-title {
    font-size: 1.5rem;
  }

  .orders-stats {
    grid-template-columns: 1fr;
  }

  .order-item {
    padding: 1rem;
  }

  .order-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .order-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .order-actions {
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .status-select {
    flex: 1;
    min-width: 120px;
  }

  .edit-order-btn,
  .delete-order-btn {
    flex: 1;
    min-width: 80px;
    font-size: 0.7rem;
    padding: 0.4rem 0.6rem;
  }

  .name-inputs,
  .address-inputs {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .cancel-btn,
  .save-btn {
    width: 100%;
  }
}