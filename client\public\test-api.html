<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EatZone API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #e55a2b;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍽️ EatZone API Connection Test</h1>
        <p>This page tests the connection to the EatZone API to help diagnose category loading issues.</p>
        
        <div id="results"></div>
        
        <button onclick="testAPI()">Test API Connection</button>
        <button onclick="testCategories()">Test Categories Endpoint</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="debug-info" class="test-result info" style="margin-top: 20px;">
            <h3>Environment Information:</h3>
            <pre id="env-info"></pre>
        </div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');
        const envInfoDiv = document.getElementById('env-info');
        
        // Display environment information
        function showEnvironmentInfo() {
            const envInfo = {
                userAgent: navigator.userAgent,
                location: window.location.href,
                timestamp: new Date().toISOString(),
                protocol: window.location.protocol,
                host: window.location.host
            };
            envInfoDiv.textContent = JSON.stringify(envInfo, null, 2);
        }
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function testAPI() {
            addResult('🔄 Testing basic API connection...', 'info');
            
            const apiUrl = 'https://eatzone.onrender.com';
            
            try {
                const response = await fetch(`${apiUrl}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ API Health Check Successful<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ API Health Check Failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ API Connection Error: ${error.message}`, 'error');
            }
        }
        
        async function testCategories() {
            addResult('🔄 Testing categories endpoint...', 'info');
            
            const apiUrl = 'https://eatzone.onrender.com';
            
            try {
                const response = await fetch(`${apiUrl}/api/category/list`);
                const data = await response.json();
                
                if (response.ok) {
                    if (data.success && data.data) {
                        addResult(`✅ Categories Loaded Successfully<br>Found ${data.data.length} categories<br><pre>${JSON.stringify(data.data.map(cat => ({name: cat.name, image: cat.image})), null, 2)}</pre>`, 'success');
                    } else {
                        addResult(`⚠️ Categories Response Issue<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                    }
                } else {
                    addResult(`❌ Categories Request Failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Categories Connection Error: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        showEnvironmentInfo();
        
        // Auto-test on page load
        setTimeout(() => {
            testAPI();
            setTimeout(() => {
                testCategories();
            }, 1000);
        }, 500);
    </script>
</body>
</html>
