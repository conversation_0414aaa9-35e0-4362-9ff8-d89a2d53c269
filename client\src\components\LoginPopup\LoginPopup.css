.login-popup{
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: #00000090;
    display: grid;
}
.login-popup-container{
    place-self: center;
    width: max(23vw,330px);
    color: #808080;
    background-color: white;
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding: 25px 30px;
    border-radius: 8px;
    font-size: 14px;
    animation: fadeIn 0.5s;
}
.login-popup-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: black;
}
.login-popup-title img{
    width: 16px;
    cursor: pointer;
}
.login-popup-inputs{
    display: flex;
    flex-direction: column;
    gap: 20px;
}
.login-popup-inputs input{
    outline: none;
    border: 1px solid #c9c9c9;
    padding: 10px;
    border-radius: 4px;
}
.login-popup-container button{
    border: none;
    padding: 10px;
    border-radius: 4px;
    color: white;
    background-color: tomato;
    font-size: 15px;
    cursor: pointer;
}
.login-popup-condition{
    display: flex;
    align-items: start;
    gap: 8px;
    margin-top: -15px;
}
.login-popup-condition input{
    margin: 5px;
}
.login-popup p span{
    color: tomato;
    font-weight: 500;
    cursor: pointer;
}

.login-popup-divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 10px 0;
}

.login-popup-divider::before,
.login-popup-divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #e0e0e0;
}

.login-popup-divider span {
    padding: 0 10px;
    color: #808080;
    font-size: 14px;
}

.google-signin-button {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    text-decoration: none;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    color: #444;
    font-weight: 500;
    transition: background-color 0.3s;
    background-color: white;
    width: 100%;
}

.google-signin-button:hover {
    background-color: #f5f5f5;
}

.google-signin-button span {
    font-size: 14px;
}