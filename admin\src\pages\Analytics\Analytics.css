.analytics {
  padding: 2rem;
  background: var(--light-gray);
  min-height: calc(100vh - 80px);
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.analytics-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0 0 0.5rem 0;
}

.analytics-header p {
  color: var(--dark-gray);
  font-size: 1rem;
  margin: 0;
}

.time-range-selector {
  display: flex;
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 0.25rem;
  box-shadow: var(--shadow-sm);
}

.time-range-selector button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: var(--dark-gray);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.time-range-selector button.active,
.time-range-selector button:hover {
  background: var(--primary-red);
  color: var(--white);
}

.analytics-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
  overflow: hidden;
  min-width: 0;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.metric-icon.revenue {
  background: linear-gradient(135deg, var(--success), #218838);
}

.metric-icon.orders {
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
}

.metric-icon.customers {
  background: linear-gradient(135deg, var(--info), #138496);
}

.metric-icon.completion {
  background: linear-gradient(135deg, var(--warning), #e0a800);
}

.metric-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.metric-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0;
  line-height: 1.2;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.metric-info p {
  color: var(--dark-gray);
  font-size: 0.875rem;
  margin: 0.25rem 0;
}

.metric-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.metric-change.positive {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.metric-change.negative {
  background: rgba(220, 53, 69, 0.1);
  color: var(--danger);
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-container {
  height: 400px;
}

/* Sales Chart */
.sales-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 300px;
  padding: 1rem 0;
  gap: 1rem;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
}

.bar {
  width: 100%;
  max-width: 40px;
  background: linear-gradient(to top, var(--primary-red), var(--primary-red-hover));
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  margin-bottom: 0.5rem;
  transition: var(--transition);
  min-height: 20px;
}

.bar:hover {
  opacity: 0.8;
}

.bar-label {
  font-size: 0.75rem;
  color: var(--dark-gray);
  margin-bottom: 0.25rem;
}

.bar-value {
  font-size: 0.625rem;
  color: var(--accent-black);
  font-weight: 600;
}

/* Category Chart */
.category-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.category-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  align-items: center;
  gap: 1rem;
}

.category-info {
  display: flex;
  flex-direction: column;
}

.category-name {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
}

.category-revenue {
  font-size: 0.75rem;
  color: var(--dark-gray);
}

.category-bar {
  height: 8px;
  background: var(--medium-gray);
  border-radius: 4px;
  overflow: hidden;
}

.category-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary-red), var(--primary-red-hover));
  transition: width 0.5s ease;
}

.category-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--accent-black);
  min-width: 40px;
  text-align: right;
}

/* Bottom Section */
.bottom-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

/* Top Items Table */
.top-items-table {
  display: flex;
  flex-direction: column;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 60px 1fr 120px 120px;
  align-items: center;
  padding: 1rem 0;
  gap: 1rem;
}

.table-header {
  border-bottom: 2px solid var(--medium-gray);
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
}

.table-row {
  border-bottom: 1px solid var(--medium-gray);
  transition: var(--transition);
}

.table-row:hover {
  background: var(--light-gray);
}

.table-row:last-child {
  border-bottom: none;
}

.rank {
  font-weight: 600;
  color: var(--primary-red);
}

.item-name {
  font-weight: 500;
  color: var(--accent-black);
}

.quantity,
.revenue {
  font-weight: 600;
  color: var(--accent-black);
  text-align: right;
}

/* Order Status */
.order-status {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.status-circle.completed {
  background: rgba(40, 167, 69, 0.2);
  border: 3px solid var(--success);
}

.status-circle.pending {
  background: rgba(255, 193, 7, 0.2);
  border: 3px solid var(--warning);
}

.status-circle.cancelled {
  background: rgba(220, 53, 69, 0.2);
  border: 3px solid var(--danger);
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-count {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-black);
  line-height: 1;
}

.status-label {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-top: 0.25rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .charts-section,
  .bottom-section {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .analytics {
    padding: 1rem;
  }
  
  .analytics-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .analytics-header h1 {
    font-size: 1.5rem;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-card {
    padding: 1rem;
  }
  
  .sales-chart {
    height: 200px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 40px 1fr 80px 80px;
    font-size: 0.75rem;
  }
  
  .chart-container {
    height: auto;
  }
}
