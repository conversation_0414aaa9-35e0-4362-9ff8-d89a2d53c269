.navbar{
    padding: 20px 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar .logo{
    width: 150px;
}

.navbar-menu{
    display: flex;
    list-style: none;
    gap: 20px;
    color: #49557e;
    font-size: 18px;
}

.navbar-right{
    display: flex;
    align-items: center;
    gap: 40px;
}

.navbar button{
    background: transparent;
    font-size: 16px;
    color: #49557e;
    border: 1px solid tomato;
    padding: 10px 30px;
    border-radius: 50px;
    cursor: pointer;
    transition: 0.3s;
}

.navbar button:hover{
    background-color: #fff4f2;
}

.navbar .active{
    padding-bottom: 2px;
    border-bottom: 2px solid #49557e;
}

.navbar li{
    cursor: pointer;
}

/* Search functionality styles */
.navbar-search {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.navbar-search input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    width: 250px;
    transition: all 0.3s ease;
}

.navbar-search input:focus {
    border-color: tomato;
    box-shadow: 0 0 5px rgba(255, 99, 71, 0.3);
}

.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    margin-top: 5px;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-image {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
}

.search-result-info {
    flex: 1;
}

.search-result-info h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.search-result-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-type {
    background: #e8f5e8;
    color: #2d5a2d;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
}

.result-detail {
    font-size: 11px;
}

.search-result-footer {
    padding: 8px 12px;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #f0f0f0;
}

.search-result-footer p {
    margin: 0;
    font-size: 11px;
    color: #888;
}

.search-add-to-cart {
    background: tomato;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.search-add-to-cart:hover {
    background: #ff4500;
    transform: scale(1.1);
}

.search-add-to-cart img {
    width: 16px;
    height: 16px;
}

.navbar-search-icon{
    position: relative;
}
.navbar-search-icon .dot{
    position: absolute;
    min-width: 10px;
    min-height: 10px;
    background-color: tomato;
    border-radius: 5px;
    top: -8px;
    right: -8px;
}
@media (max-width:1050px){
    .navbar .logo{
        width: 140px;
    }
    .navbar-menu{
        gap: 20px;
        font-size: 17px;
    }
    .navbar-right{
        gap: 30px;
    }
    .navbar-right img{
        width: 22px;
    }
    .navbar-right button{
        padding: 8px 15px;
    }
    .navbar-search input {
        width: 200px;
    }
}
@media (max-width:900px){
    .navbar .logo{
        width: 120px;
    }
    .navbar-menu{
        gap: 20px;
        font-size: 16px;
    }
    .navbar-right{
        gap: 20px;
    }
    .navbar-right img{
        width: 20px;
    }
    .navbar-right button{
        padding: 7px 20px;
        font-size: 15px
        ;
    }
    .navbar-search input {
        width: 180px;
        font-size: 13px;
    }
}
@media (max-width:750px){
     .navbar-menu{
        display: none;
    }
    .navbar-search input {
        width: 140px;
        font-size: 12px;
        padding: 6px 8px;
    }
    .navbar-search img {
        width: 16px;
    }
    .navbar-right {
        gap: 8px;
    }
    .navbar-right img {
        width: 18px;
    }
    .navbar-right button {
        padding: 6px 12px;
        font-size: 12px;
    }
    .search-result-item {
        padding: 8px;
        gap: 8px;
    }
    .search-result-image {
        width: 30px;
        height: 30px;
    }
    .search-result-info h4 {
        font-size: 12px;
    }
    .search-result-info p {
        font-size: 10px;
    }
    .search-add-to-cart {
        width: 28px;
        height: 28px;
    }
    .search-add-to-cart img {
        width: 14px;
        height: 14px;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 8px 4px;
        flex-wrap: nowrap;
    }
    .navbar .logo {
        width: 100px;
    }
    .navbar-search input {
        width: 120px;
        font-size: 11px;
        padding: 5px 6px;
    }
    .navbar-search img {
        width: 14px;
    }
    .navbar-right {
        gap: 6px;
    }
    .navbar-right img {
        width: 16px;
    }
    .navbar-right button {
        padding: 5px 8px;
        font-size: 11px;
        white-space: nowrap;
    }
    .profile-dropdown {
        right: 0;
        left: auto;
        min-width: 140px;
    }
}

@media (max-width: 360px) {
    .navbar {
        padding: 6px 2px;
        min-height: 50px;
    }
    .navbar .logo {
        width: 85px;
    }
    .navbar-search {
        flex: 1;
        max-width: 90px;
    }
    .navbar-search input {
        width: 100%;
        font-size: 10px;
        padding: 4px 5px;
    }
    .navbar-search img {
        width: 12px;
    }
    .navbar-right {
        gap: 3px;
        flex-shrink: 0;
    }
    .navbar-right img {
        width: 14px;
    }
    .navbar-right button {
        padding: 4px 6px;
        font-size: 10px;
        white-space: nowrap;
    }
    .profile-dropdown {
        min-width: 120px;
        font-size: 12px;
        right: 0;
        left: auto;
    }
    .search-results {
        width: calc(100vw - 20px);
        max-width: 300px;
        right: 0;
        left: auto;
    }
}