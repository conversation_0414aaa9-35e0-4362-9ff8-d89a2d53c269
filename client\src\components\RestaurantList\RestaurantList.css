.restaurant-list-container {
  max-width: 1200px;
  margin: 40px auto;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.restaurant-list-header {
  margin-bottom: 32px;
  text-align: center;
  position: relative;
}

.header-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.restaurant-list-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0 0 8px 0;
}

.restaurant-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0 0 12px 0;
  font-style: italic;
}

.header-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.restaurant-count {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.view-all-btn {
  background: #333;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  background: #555;
  transform: translateY(-1px);
}

.scroll-hint {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scroll-hint span {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  animation: pulse 2s infinite;
  background: rgba(102, 102, 102, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(102, 102, 102, 0.2);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.98);
  }
}

.restaurant-grid {
  display: flex;
  overflow-x: auto;
  gap: 16px;
  margin-top: 24px;
  padding: 0 8px 16px 8px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.restaurant-grid {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.restaurant-grid::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Section Divider */
.section-divider {
  display: none;
}

/* Loading States */
.restaurant-loading {
  margin-top: 20px;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.restaurant-card-skeleton {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.skeleton-image {
  width: 100%;
  height: 160px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-content {
  padding: 12px;
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 12px;
}

.skeleton-description {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 80%;
}

.skeleton-details {
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  width: 60%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Error State */
.restaurant-error {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-top: 20px;
}

.restaurant-error p {
  font-size: 16px;
  color: #666;
  margin: 0 0 16px 0;
}

.retry-btn {
  background: #333;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #555;
}

/* No Restaurants State */
.no-restaurants {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-top: 20px;
}

.no-restaurants p {
  font-size: 16px;
  color: #666;
  margin: 0 0 8px 0;
}

.no-restaurants p:first-child {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .restaurant-list-container {
    padding: 18px;
    margin: 30px auto;
  }

  .restaurant-grid {
    gap: 14px;
    padding: 0 6px 14px 6px;
  }

  .loading-grid {
    gap: 14px;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .restaurant-list-container {
    padding: 10px;
    margin: 16px auto;
  }

  .restaurant-list-header h2 {
    font-size: 22px;
  }

  .restaurant-subtitle {
    font-size: 14px;
  }

  .restaurant-grid {
    gap: 10px;
    padding: 0 2px 10px 2px;
  }

  .restaurant-grid::-webkit-scrollbar {
    display: none;
  }

  .loading-grid {
    gap: 10px;
  }

  .header-bottom {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .restaurant-count {
    font-size: 13px;
  }

  .view-all-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .restaurant-list-container {
    padding: 8px;
    margin: 12px auto;
  }

  .restaurant-list-header h2 {
    font-size: 20px;
  }

  .restaurant-subtitle {
    font-size: 13px;
  }

  .restaurant-grid {
    gap: 8px;
    padding: 0 1px 8px 1px;
  }

  .restaurant-count {
    font-size: 12px;
  }

  .view-all-btn {
    padding: 5px 10px;
    font-size: 10px;
  }
}

@media (max-width: 360px) {
  .restaurant-list-container {
    padding: 6px;
    margin: 10px auto;
  }

  .restaurant-list-header h2 {
    font-size: 18px;
  }

  .restaurant-subtitle {
    font-size: 12px;
  }

  .restaurant-grid {
    gap: 6px;
    padding: 0 0 6px 0;
  }

  .restaurant-count {
    font-size: 11px;
  }

  .view-all-btn {
    padding: 4px 8px;
    font-size: 9px;
  }
}

@media (max-width: 480px) {
  .restaurant-list-container {
    padding: 8px;
    margin: 15px auto;
  }

  .restaurant-list-header h2 {
    font-size: 20px;
  }

  .restaurant-subtitle {
    font-size: 13px;
  }

  .restaurant-grid {
    gap: 10px;
    padding: 0 2px 10px 2px;
  }

  .restaurant-grid::-webkit-scrollbar {
    height: 4px;
  }

  .loading-grid {
    gap: 10px;
  }

  .header-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .restaurant-count {
    font-size: 12px;
  }

  .view-all-btn {
    padding: 5px 10px;
    font-size: 10px;
  }
}
