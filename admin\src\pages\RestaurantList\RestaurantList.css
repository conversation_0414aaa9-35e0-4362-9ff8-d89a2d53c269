.restaurant-list {
  width: 70%;
  margin-left: 5vw;
  margin-top: 50px;
  color: #6d6d6d;
  font-size: 16px;
}

.restaurant-list-header {
  margin-bottom: 30px;
}

.restaurant-list-header h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 8px;
}

.restaurant-list-header p {
  color: #666;
  font-size: 14px;
}

.restaurant-list-table {
  border: 1px solid #cacaca;
  border-radius: 8px;
  overflow: hidden;
}

.restaurant-list-table-format {
  display: grid;
  grid-template-columns: 0.5fr 1.5fr 2fr 2fr 1fr 0.8fr 1fr;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border-bottom: 1px solid #cacaca;
  font-size: 13px;
}

.restaurant-list-table-format.title {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.restaurant-list-table-format:last-child {
  border-bottom: none;
}

.restaurant-list-table-format img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
}

.restaurant-list-table-format p {
  margin: 0;
  line-height: 1.4;
}

.restaurant-list-table-format .description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 12px;
}

.restaurant-list-table-format .address {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 12px;
}

.delivery-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.delivery-info p {
  font-size: 11px;
  margin: 0;
}

.rating {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.cursor {
  cursor: pointer;
  font-size: 16px;
  text-align: center;
  transition: transform 0.2s;
}

.cursor:hover {
  transform: scale(1.2);
}

.no-restaurants {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-restaurants p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .restaurant-list {
    width: 90%;
    margin-left: 5%;
    margin-top: 30px;
  }
  
  .restaurant-list-table-format {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 15px;
    text-align: left;
  }
  
  .restaurant-list-table-format.title {
    display: none;
  }
  
  .restaurant-list-table-format {
    border-bottom: 2px solid #e9ecef;
    background: white;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .restaurant-list-table-format img {
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
  }
  
  .restaurant-list-table-format p {
    margin-bottom: 5px;
  }
  
  .restaurant-list-table-format .description,
  .restaurant-list-table-format .address {
    -webkit-line-clamp: 3;
    font-size: 13px;
  }
  
  .delivery-info {
    flex-direction: row;
    gap: 10px;
  }
  
  .cursor {
    align-self: flex-end;
    margin-top: 10px;
  }

  .restaurant-actions {
    justify-content: flex-end;
    margin-top: 10px;
  }
}

/* Action buttons */
.restaurant-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #f5f5f5;
}

.edit-btn {
  color: #007bff;
}

.edit-btn:hover {
  background-color: #e3f2fd;
  color: #0056b3;
}

.delete-btn {
  color: #dc3545;
}

.delete-btn:hover {
  background-color: #ffebee;
  color: #c82333;
}

/* Edit Modal */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.edit-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

.edit-form {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group small {
  display: block;
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-row.three-cols {
  grid-template-columns: 1fr 1fr 1fr;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.btn-cancel,
.btn-save {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
}

.btn-cancel:hover {
  background-color: #e9ecef;
  color: #333;
}

.btn-save {
  background-color: #007bff;
  color: white;
}

.btn-save:hover {
  background-color: #0056b3;
}

/* Mobile responsive for modal */
@media (max-width: 768px) {
  .edit-modal {
    width: 95%;
    margin: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-save {
    width: 100%;
  }
}
