[build]
  command = "npm ci && npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--production=false"
  VITE_API_BASE_URL = "https://eatzone.onrender.com"
  VITE_APP_ENV = "production"
  NODE_ENV = "production"

# Redirect all routes to index.html for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Prevent service worker requests from returning HTML
[[redirects]]
  from = "/sw.js"
  to = "/404"
  status = 404

[[redirects]]
  from = "/*/sw.js"
  to = "/404"
  status = 404

# Environment variables for production build (merged with build section above)
# VITE_API_BASE_URL = "https://eatzone.onrender.com"
# VITE_APP_ENV = "production"
# NODE_ENV = "production"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://eatzone.onrender.com https://res.cloudinary.com; frame-src 'none';"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
  NODE_ENV = "production"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"