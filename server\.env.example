# JWT Secret for authentication (Generate a strong random string)
JWT_SECRET=your_jwt_secret_here

# SECURITY: Super Admin Key (Required for admin registration)
SUPER_ADMIN_KEY=your_super_admin_key_here

# Google OAuth credentials
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Frontend and Admin URLs
FRONTEND_URL=http://localhost:5173
ADMIN_URL=http://localhost:5175

# Server configuration
SERVER_PORT=4000
SERVER_URL=http://localhost:4000

# MongoDB connection
MONGODB_URI=your_mongodb_connection_string

# Stripe API Key (use test key for development)
STRIPE_SECRET_KEY=your_stripe_secret_key

# Gemini API Key for chatbot
GEMINI_API_KEY=your_gemini_api_key

# Cart Security Configuration
MAX_CART_QUANTITY_PER_ITEM=50
MAX_TOTAL_CART_ITEMS=200

# SECURITY INSTRUCTIONS:
# 1. Copy this file to .env
# 2. Replace all placeholder values with your actual credentials
# 3. Never commit the .env file to version control
# 4. Keep your credentials secure and rotate them regularly
# 5. The SUPER_ADMIN_KEY is required for creating admin accounts
