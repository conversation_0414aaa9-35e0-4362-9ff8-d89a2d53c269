.chatbot-container {
  width: 100%;
  max-width: 600px;
  height: 80vh;
  margin: 50px auto;
  border: 2px solid #ddd;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  font-family: "Segoe UI", sans-serif;
  background: #fefefe;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
}

.chat-header {
  padding: 20px;
  font-size: 20px;
  font-weight: bold;
  background-color: #f44336;
  color: white;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  text-align: center;
}

.chat-window {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #fafafa;
}

.chat-bubble {
  max-width: 80%;
  margin-bottom: 10px;
  padding: 12px 15px;
  border-radius: 18px;
  line-height: 1.4;
  font-size: 15px;
}

.chat-bubble.user {
  background-color: #e0f7fa;
  align-self: flex-end;
  border-bottom-right-radius: 5px;
}

.chat-bubble.bot {
  background-color: #eee;
  align-self: flex-start;
  border-bottom-left-radius: 5px;
}

.chat-input-container {
  display: flex;
  padding: 15px;
  border-top: 1px solid #ddd;
}

.chat-input-container input {
  flex: 1;
  padding: 12px;
  border-radius: 25px;
  border: 1px solid #ccc;
  outline: none;
  margin-right: 10px;
  font-size: 14px;
}

.chat-input-container button {
  padding: 12px 20px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
}

.chat-input-container button:hover {
  background-color: #d32f2f;
}
