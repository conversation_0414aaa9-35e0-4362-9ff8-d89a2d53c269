.debug-panel-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: #ff6b35;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: all 0.3s ease;
}

.debug-panel-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.debug-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  max-height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON>goe UI', Robot<PERSON>, sans-serif;
}

.debug-panel-header {
  background: #ff6b35;
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.debug-panel-header button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.debug-panel-header button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.debug-panel-content {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.debug-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.debug-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.debug-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.debug-button {
  background: #ff6b35;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
}

.debug-button:hover:not(:disabled) {
  background: #e55a2b;
  transform: translateY(-1px);
}

.debug-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.test-results {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.test-results h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.summary {
  margin-bottom: 12px;
}

.summary p {
  margin: 4px 0;
  font-size: 11px;
  color: #666;
}

.detailed-results {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 10px;
  align-items: center;
}

.result-item.success {
  background: #d4edda;
  color: #155724;
}

.result-item.error {
  background: #f8d7da;
  color: #721c24;
}

.result-name {
  font-weight: 500;
}

.result-time {
  font-family: monospace;
  font-size: 9px;
}

.result-status {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 8px;
}

.cache-stats {
  margin-bottom: 12px;
}

.cache-stats p {
  margin: 4px 0;
  font-size: 11px;
  color: #666;
  font-family: monospace;
}

.tips-list {
  margin: 0;
  padding-left: 16px;
}

.tips-list li {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .debug-panel {
    width: calc(100vw - 40px);
    max-width: 350px;
  }
  
  .debug-panel-toggle {
    bottom: 80px; /* Avoid conflict with mobile navigation */
  }
}
