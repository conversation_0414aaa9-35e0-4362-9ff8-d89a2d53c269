{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:debug": "vite build --mode production --debug", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "axios": "^1.9.0", "cloudinary": "^2.7.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}