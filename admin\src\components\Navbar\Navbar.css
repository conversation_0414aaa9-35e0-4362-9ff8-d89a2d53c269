.navbar {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: var(--white);
  border-bottom: 1px solid var(--medium-gray);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: 70px;
  max-height: 80px;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  justify-self: start;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  color: var(--dark-gray);
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.mobile-menu-btn:hover {
  background: var(--light-gray);
  color: var(--accent-black);
}

.navbar .logo {
  width: 100px;
  height: auto;
  flex-shrink: 0;
}

.navbar-title {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  justify-self: center;
  min-width: 0;
}

.navbar-title h2 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.navbar-title span {
  font-size: 0.75rem;
  color: var(--dark-gray);
  display: block;
  margin-top: 0.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-self: end;
}

.navbar-stats {
  display: flex;
  gap: 1.5rem;
  padding: 0.5rem 1rem;
  background: var(--light-gray);
  border-radius: var(--radius-lg);
}

.stat-item {
  text-align: center;
  min-width: 0;
}

.stat-label {
  display: block;
  font-size: 0.625rem;
  color: var(--dark-gray);
  margin-bottom: 0.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-value {
  display: block;
  font-size: 1rem;
  font-weight: 700;
  color: var(--primary-red);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.navbar-notifications {
  position: relative;
}

.notification-icon {
  position: relative;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  background: var(--light-gray);
  color: var(--dark-gray);
  cursor: pointer;
  transition: var(--transition);
}

.notification-icon:hover {
  background: var(--medium-gray);
  color: var(--accent-black);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--primary-red);
  color: var(--white);
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  min-width: 18px;
  text-align: center;
}

.navbar-profile {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition);
  min-width: 0;
  max-width: 200px;
}

.navbar-profile:hover {
  background: var(--light-gray);
}

.profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 0.875rem;
  overflow: hidden;
  flex-shrink: 0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.profile-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.profile-name {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.profile-role {
  font-size: 0.65rem;
  color: var(--dark-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.dropdown-arrow {
  transition: var(--transition);
  color: var(--dark-gray);
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  overflow: hidden;
  z-index: 1000;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--accent-black);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: var(--light-gray);
}

.dropdown-item.logout {
  color: var(--danger);
}

.dropdown-item.logout:hover {
  background: rgba(220, 53, 69, 0.1);
}

.dropdown-divider {
  height: 1px;
  background: var(--medium-gray);
  margin: 0.5rem 0;
}

/* Responsive */
@media (max-width: 1200px) {
  .navbar-stats {
    gap: 1rem;
    padding: 0.375rem 0.75rem;
  }

  .stat-label {
    font-size: 0.6rem;
  }

  .stat-value {
    font-size: 0.9rem;
  }
}

@media (max-width: 1024px) {
  .navbar {
    padding: 0.75rem 1.25rem;
  }

  .navbar-left {
    gap: 0.5rem;
  }

  .navbar .logo {
    width: 90px;
  }

  .navbar-title h2 {
    font-size: 1.125rem;
  }

  .navbar-title span {
    font-size: 0.7rem;
  }

  .navbar-stats {
    gap: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .stat-label {
    font-size: 0.55rem;
  }

  .stat-value {
    font-size: 0.85rem;
  }

  .profile-name {
    font-size: 0.75rem;
  }

  .profile-role {
    font-size: 0.6rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 1rem;
    grid-template-columns: auto 1fr auto;
  }

  .navbar-stats {
    display: none;
  }

  .navbar-title h2 {
    font-size: 1rem;
  }

  .navbar-title span {
    display: none;
  }

  .profile-info {
    display: none;
  }

  .navbar-right {
    gap: 0.75rem;
  }

  .dropdown-arrow {
    display: none;
  }
}

@media (max-width: 640px) {
  .navbar {
    padding: 0.625rem 0.875rem;
  }

  .navbar-left {
    gap: 0.375rem;
  }

  .navbar .logo {
    width: 80px;
  }

  .navbar-title h2 {
    font-size: 0.95rem;
  }

  .profile-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .notification-icon {
    padding: 0.375rem;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 0.5rem 0.75rem;
    min-height: 60px;
    grid-template-columns: auto 1fr auto;
  }

  .mobile-menu-btn {
    display: block;
    padding: 0.375rem;
  }

  .navbar-left {
    gap: 0.25rem;
  }

  .navbar .logo {
    width: 70px;
  }

  .navbar-title h2 {
    font-size: 0.875rem;
  }

  .navbar-right {
    gap: 0.5rem;
  }

  .profile-avatar {
    width: 30px;
    height: 30px;
    font-size: 0.75rem;
  }

  .notification-icon {
    padding: 0.25rem;
  }

  .notification-icon svg {
    width: 18px;
    height: 18px;
  }

  .notification-badge {
    font-size: 0.55rem;
    padding: 0.1rem 0.3rem;
    min-width: 16px;
  }
}

@media (max-width: 360px) {
  .navbar {
    padding: 0.5rem;
    min-height: 55px;
    grid-template-columns: auto 1fr auto;
  }

  .navbar .logo {
    width: 60px;
  }

  .navbar-title h2 {
    font-size: 0.8rem;
  }

  .profile-avatar {
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
  }

  .notification-icon {
    padding: 0.2rem;
  }

  .notification-icon svg {
    width: 16px;
    height: 16px;
  }
}