.feedback {
  padding: 2rem;
  background: var(--light-gray);
  min-height: calc(100vh - 80px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: var(--dark-gray);
  font-size: 1rem;
  margin: 0;
}

.filter-buttons {
  display: flex;
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 0.25rem;
  box-shadow: var(--shadow-sm);
  gap: 0.25rem;
}

.filter-buttons button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: var(--dark-gray);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  font-size: 0.875rem;
}

.filter-buttons button.active,
.filter-buttons button:hover {
  background: var(--primary-red);
  color: var(--white);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.stat-icon.complaints {
  background: linear-gradient(135deg, var(--danger), #c82333);
}

.stat-icon.feedback-positive {
  background: linear-gradient(135deg, var(--success), #218838);
}

.stat-icon.suggestions {
  background: linear-gradient(135deg, var(--info), #138496);
}

.stat-icon.average-rating {
  background: linear-gradient(135deg, var(--warning), #e0a800);
}

.stat-info h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0;
  line-height: 1;
}

.stat-info p {
  color: var(--dark-gray);
  font-size: 0.875rem;
  margin: 0.25rem 0 0 0;
}

/* Feedback Table */
.feedback-table {
  display: flex;
  flex-direction: column;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr 1fr 0.8fr 0.8fr 1fr 1fr;
  align-items: center;
  padding: 1rem;
  background: var(--light-gray);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
  gap: 1rem;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr 1fr 0.8fr 0.8fr 1fr 1fr;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--medium-gray);
  transition: var(--transition);
  gap: 1rem;
}

.table-row:hover {
  background: var(--light-gray);
}

.table-row:last-child {
  border-bottom: none;
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Customer Info */
.customer-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
}

.customer-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0;
}

.customer-info p {
  font-size: 0.75rem;
  color: var(--dark-gray);
  margin: 0.25rem 0 0 0;
}

/* Type Info */
.type-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--dark-gray);
}

.type-label {
  font-size: 0.75rem;
  text-transform: capitalize;
  font-weight: 500;
}

/* Subject Info */
.subject-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0 0 0.25rem 0;
}

.subject-info p {
  font-size: 0.75rem;
  color: var(--dark-gray);
  margin: 0;
}

/* Rating Info */
.rating-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.rating-stars {
  display: flex;
  gap: 0.125rem;
}

.star-filled {
  color: var(--warning);
}

.star-empty {
  color: var(--medium-gray);
}

.rating-value {
  font-size: 0.75rem;
  color: var(--dark-gray);
}

/* Priority Info */
.priority-info .badge {
  text-transform: capitalize;
}

/* Status Info */
.status-info .badge {
  text-transform: capitalize;
}

/* Date Info */
.date-info p {
  font-size: 0.75rem;
  color: var(--accent-black);
  margin: 0.125rem 0;
}

.date-info p:first-child {
  font-weight: 600;
}

/* Actions */
.actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.btn-icon.view {
  background: rgba(23, 162, 184, 0.1);
  color: var(--info);
}

.btn-icon.view:hover {
  background: var(--info);
  color: var(--white);
}

.btn-icon.resolve {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.btn-icon.resolve:hover {
  background: var(--success);
  color: var(--white);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.feedback-modal {
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content {
  padding: 1.5rem;
}

.feedback-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.detail-row {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 1rem;
  align-items: start;
}

.detail-row label {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
}

.detail-row span {
  color: var(--dark-gray);
  font-size: 0.875rem;
}

.type-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--light-gray);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message-content {
  background: var(--light-gray);
  padding: 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  line-height: 1.5;
}

.response-form {
  border-top: 1px solid var(--medium-gray);
  padding-top: 1.5rem;
}

.existing-response {
  border-top: 1px solid var(--medium-gray);
  padding-top: 1.5rem;
}

.existing-response h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0 0 1rem 0;
}

.response-content {
  background: var(--light-gray);
  padding: 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.response-date {
  font-size: 0.75rem;
  color: var(--dark-gray);
  font-style: italic;
  margin: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .table-header,
  .table-row {
    grid-template-columns: 2fr 1fr 2fr 1fr 1fr;
    font-size: 0.75rem;
  }
  
  .priority-info,
  .date-info,
  .actions {
    display: none;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .feedback {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr auto;
    gap: 0.5rem;
  }
  
  .type-info,
  .rating-info {
    display: none;
  }
  
  .customer-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .feedback-modal {
    width: 95%;
    margin: 1rem;
  }
  
  .detail-row {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
