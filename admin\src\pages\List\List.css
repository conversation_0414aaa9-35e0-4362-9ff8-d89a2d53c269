.list {
  padding: 2rem;
  background: var(--light-gray);
  min-height: calc(100vh - 80px);
}

.list .card {
  max-width: 1200px;
  margin: 0 auto;
}

.list .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list .card-title {
  font-size: 1.75rem;
  color: var(--accent-black);
}

.list .card-subtitle {
  color: var(--dark-gray);
  margin-top: 0.5rem;
}

.list-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.list-stat {
  text-align: center;
}

.list-stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-red);
  display: block;
}

.list-stat-label {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-top: 0.25rem;
}

.list-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  background: var(--white);
  font-size: 0.875rem;
  cursor: pointer;
}

.search-input {
  flex: 1;
  min-width: 250px;
  padding: 0.5rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  background: var(--white);
  font-size: 0.875rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.list-table {
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.list-table-format {
  display: grid;
  grid-template-columns: 80px 2fr 1fr 1fr 120px 1fr 100px;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--medium-gray);
  font-size: 0.875rem;
  transition: var(--transition);
}

.list-table-format:hover {
  background: var(--light-gray);
}

.list-table-format:last-child {
  border-bottom: none;
}

.list-table-format.title {
  background: var(--light-gray);
  font-weight: 600;
  color: var(--accent-black);
  border-bottom: 2px solid var(--medium-gray);
}

.list-table-format.title:hover {
  background: var(--light-gray);
}

.food-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.food-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.food-name {
  font-weight: 600;
  color: var(--accent-black);
  margin: 0;
}

.food-description {
  font-size: 0.75rem;
  color: var(--dark-gray);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.food-restaurant {
  font-size: 0.75rem;
  color: var(--dark-gray);
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  background: var(--light-gray);
  border-radius: var(--radius-sm);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.food-category {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--primary-red-light);
  color: var(--primary-red);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.food-price {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.food-price-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.price-with-discount {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.original-price {
  color: #999;
  font-size: 0.75rem;
  text-decoration: line-through;
  font-weight: 400;
}

.discounted-price {
  color: var(--primary-red);
  font-size: 1rem;
  font-weight: 700;
}

.discount-badge-small {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: white;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 0.625rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.item-tag {
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 0.625rem;
  font-weight: 600;
  text-align: center;
  width: fit-content;
}

.item-tag.popular {
  background: linear-gradient(135deg, #ff9500, #ff8c00);
  color: white;
}

.item-tag.featured {
  background: linear-gradient(135deg, #ffd700, #ffcc00);
  color: #333;
}

.food-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.edit-btn {
  background: rgba(23, 162, 184, 0.1);
  color: var(--info);
}

.edit-btn:hover {
  background: var(--info);
  color: var(--white);
}

.delete-btn {
  background: rgba(220, 53, 69, 0.1);
  color: var(--danger);
}

.delete-btn:hover {
  background: var(--danger);
  color: var(--white);
}

.no-items {
  text-align: center;
  padding: 3rem;
  color: var(--dark-gray);
}

.no-items svg {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
  color: var(--medium-gray);
}

.no-items h3 {
  font-size: 1.25rem;
  color: var(--accent-black);
  margin: 0 0 0.5rem 0;
}

.no-items p {
  margin: 0;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 1rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .list-table-format {
    grid-template-columns: 60px 2fr 1fr 80px;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
  }

  .food-category,
  .food-restaurant,
  .food-description {
    display: none;
  }

  .list-filters {
    flex-direction: column;
  }

  .search-input {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .list {
    padding: 1rem;
  }

  .list .card-title {
    font-size: 1.5rem;
  }

  .list-stats {
    justify-content: space-around;
    gap: 1rem;
  }

  .list-table-format {
    grid-template-columns: 50px 1fr 60px;
    gap: 0.5rem;
    padding: 0.75rem;
    font-size: 0.75rem;
  }

  .food-image {
    width: 50px;
    height: 50px;
  }

  .food-price {
    font-size: 0.875rem;
  }

  .action-btn {
    width: 32px;
    height: 32px;
  }

  .food-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Clear All Button Styles */
.card-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  border: 2px solid #2563eb;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  border: 2px solid #dc2626;
}

.btn-danger:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.btn-danger:active {
  transform: translateY(0);
}

/* Responsive for clear button */
@media (max-width: 768px) {
  .list .card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .card-actions {
    width: 100%;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

/* Edit Modal Styles */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.edit-modal {
  background: var(--white);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--medium-gray);
}

.edit-modal-header h3 {
  margin: 0;
  color: var(--accent-black);
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  color: var(--dark-gray);
  transition: var(--transition);
}

.close-btn:hover {
  background: var(--light-gray);
  color: var(--accent-black);
}

.edit-form {
  padding: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: var(--transition);
  background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group small {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--dark-gray);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--medium-gray);
}

.btn-secondary {
  background: var(--light-gray);
  color: var(--accent-black);
  border: 1px solid var(--medium-gray);
}

.btn-secondary:hover {
  background: var(--medium-gray);
}

.btn-primary {
  background: var(--primary-red);
  color: var(--white);
  border: 1px solid var(--primary-red);
}

.btn-primary:hover {
  background: #e63946;
  border-color: #e63946;
}

/* Checkbox Styles */
.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--accent-black);
  margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-red);
  cursor: pointer;
  margin: 0;
}

.checkmark {
  position: relative;
}

/* Responsive for edit modal */
@media (max-width: 768px) {
  .edit-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .edit-modal-header {
    padding: 1rem;
  }

  .edit-form {
    padding: 1rem;
  }
}