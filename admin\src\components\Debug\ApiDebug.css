.api-debug {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.debug-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.debug-header h2 {
    color: #333;
    margin: 0;
}

.refresh-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.refresh-btn:hover {
    background: #0056b3;
}

.debug-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.debug-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.debug-card h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 18px;
}

.config-item, .status-item {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-item code {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    color: #e83e8c;
}

.status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status.online, .status.working, .status.configured, .status.success {
    background: #d4edda;
    color: #155724;
}

.status.offline, .status.error {
    background: #f8d7da;
    color: #721c24;
}

.status.checking, .status.testing {
    background: #fff3cd;
    color: #856404;
}

.test-results {
    max-height: 400px;
    overflow-y: auto;
}

.test-result {
    background: #f8f9fa;
    margin-bottom: 10px;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #6c757d;
}

.test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.test-status {
    font-size: 12px;
    font-weight: bold;
}

.test-details {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
    word-break: break-all;
}

.test-time {
    font-size: 11px;
    color: #adb5bd;
}

.response-data {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    font-size: 12px;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
}

.troubleshooting-steps {
    margin: 0;
    padding-left: 20px;
}

.troubleshooting-steps li {
    margin-bottom: 15px;
    line-height: 1.5;
}

.troubleshooting-steps code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    color: #e83e8c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .debug-summary {
        grid-template-columns: 1fr;
    }
    
    .debug-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .config-item, .status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
